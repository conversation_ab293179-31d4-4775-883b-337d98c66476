﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicenseTransactionDal : EfEntityRepositoryBase<LicenseTransaction, GymContext>, ILicenseTransactionDal
    {
        // Constructor injection (Scalability için)
        public EfLicenseTransactionDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfLicenseTransactionDal() : base()
        {
        }

        public IDataResult<List<LicenseTransaction>> GetAllFiltered(int? userID, string startDate, string endDate, int page, int pageSize)
        {
            try
            {
                List<LicenseTransaction> transactions;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    transactions = _context.LicenseTransactions.Where(lt => lt.IsActive).ToList();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        transactions = context.LicenseTransactions.Where(lt => lt.IsActive).ToList();
                    }
                }

                // Apply filters
                if (userID.HasValue)
                {
                    transactions = transactions.Where(lt => lt.UserID == userID.Value).ToList();
                }

                if (!string.IsNullOrEmpty(startDate) && DateTime.TryParse(startDate, out DateTime start))
                {
                    transactions = transactions.Where(lt => lt.TransactionDate >= start).ToList();
                }

                if (!string.IsNullOrEmpty(endDate) && DateTime.TryParse(endDate, out DateTime end))
                {
                    transactions = transactions.Where(lt => lt.TransactionDate <= end.AddDays(1)).ToList();
                }

                // Sort by TransactionDate descending (most recent first)
                transactions = transactions.OrderByDescending(lt => lt.TransactionDate).ToList();

                // Apply pagination
                var totalCount = transactions.Count;
                var paginatedTransactions = transactions
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new SuccessDataResult<List<LicenseTransaction>>(paginatedTransactions, $"Toplam {totalCount} kayıt bulundu");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<LicenseTransaction>>($"Lisans işlemleri getirilirken hata oluştu: {ex.Message}");
            }
        }
    }

}
